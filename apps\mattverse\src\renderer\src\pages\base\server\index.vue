<template>
  <div class="page-layout">
    <!-- 搜索表单区域 -->
    <div class="search-section">
      <MattSearchForm
        :fields="searchFields"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
      />
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <MattTable
        :columns="tableColumns"
        :data="paginatedData"
        :loading="loading"
        :actions="tableActions"
        :show-column-settings="true"
        :row-selection="rowSelection"
        class="h-full"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 表格左上角插槽：页面标题和选中信息 -->
        <template #header-left>
          <div class="flex items-center gap-4">
            <h1 class="text-2xl text-primary font-bold">服务器状态</h1>
            <div
              v-if="rowSelection.selectedRowKeys.length > 0"
              class="text-sm text-muted-foreground"
            >
              已选择 {{ rowSelection.selectedRowKeys.length }} 台服务器
            </div>
          </div>
        </template>

        <!-- 表格右上角插槽：批量操作按钮和刷新设置 -->
        <template #header-right>
          <div class="flex items-center gap-2">
            <!-- 批量操作按钮 -->
            <template v-if="rowSelection.selectedRowKeys.length > 0">
              <Button
                v-for="action in batchActions"
                :key="action.key"
                :variant="action.variant"
                size="sm"
                class="h-9 gap-2"
                :disabled="true"
                @click="handleBatchAction(action.key)"
              >
                <MattIcon :name="action.icon" class="h-4 w-4" />
                {{ action.label }}
              </Button>
            </template>

            <!-- 刷新间隔设置 -->
            <Label class="text-sm text-muted-foreground whitespace-nowrap">刷新间隔（秒）</Label>
            <Input
              v-model.number="refreshInterval"
              type="number"
              min="1"
              max="300"
              class="w-20 h-9"
              @change="handleRefreshIntervalChange"
            />
            <Button
              variant="outline"
              size="sm"
              class="h-9 gap-2"
              @click="handleRefresh"
              :disabled="loading"
            >
              <MattIcon name="RefreshCw" class="h-4 w-4" />
              刷新
            </Button>
          </div>
        </template>

        <!-- 服务器名称列 - 支持复制 -->
        <template #serverName="{ record }">
          <div class="flex items-center gap-2">
            <span>{{ record.serverName }}</span>
            <Button
              variant="ghost"
              size="icon"
              class="h-6 w-6"
              @click.stop="copyToClipboard(record.serverName)"
            >
              <MattIcon name="Copy" class="h-3 w-3" />
            </Button>
          </div>
        </template>

        <!-- 服务器类型列 - 支持复制 -->
        <template #serverType="{ record }">
          <div class="flex items-center gap-2">
            <Badge :variant="getServerTypeBadgeVariant(record.serverType)">
              {{ getServerTypeText(record.serverType) }}
            </Badge>
            <Button
              variant="ghost"
              size="icon"
              class="h-6 w-6"
              @click.stop="copyToClipboard(record.serverType)"
            >
              <MattIcon name="Copy" class="h-3 w-3" />
            </Button>
          </div>
        </template>

        <!-- 服务器ID列 - 支持复制 -->
        <template #serverId="{ record }">
          <div class="flex items-center gap-2">
            <span class="font-mono text-xs">{{ record.serverId }}</span>
            <Button
              variant="ghost"
              size="icon"
              class="h-6 w-6"
              @click.stop="copyToClipboard(record.serverId)"
            >
              <MattIcon name="Copy" class="h-3 w-3" />
            </Button>
          </div>
        </template>

        <!-- 服务器状态列 -->
        <template #serverStatus="{ record }">
          <Badge :class="getStatusClass(record.serverStatus)">
            {{ getStatusText(record.serverStatus) }}
          </Badge>
        </template>

        <!-- 注册时间列 -->
        <template #createTime="{ record }">
          <span class="text-sm">{{ formatTimestamp(record.createTime) }}</span>
        </template>

        <!-- 更新时间列 -->
        <template #updateTime="{ record }">
          <span class="text-sm">{{ formatTimestamp(record.updateTime) }}</span>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
                <MattIcon name="MoreHorizontal" class="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" class="w-40">
              <template v-for="(action, index) in serverActions" :key="action.key">
                <!-- 分隔线 -->
                <DropdownMenuSeparator v-if="action.separator && index > 0" />

                <!-- 操作项 -->
                <DropdownMenuItem
                  @click="handleServerActionClick(action.key, record)"
                  :disabled="action.disabled?.(record)"
                  :class="
                    action.variant === 'destructive'
                      ? 'text-destructive focus:text-destructive'
                      : ''
                  "
                >
                  <MattIcon :name="action.icon" class="mr-2 h-4 w-4" />
                  {{ action.label }}
                </DropdownMenuItem>
              </template>
            </DropdownMenuContent>
          </DropdownMenu>
        </template>
      </MattTable>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <MattPagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="filteredData.length"
        :show-size-changer="true"
        :show-quick-jumper="true"
        @change="handlePageChange"
      />
    </div>

    <!-- 删除确认弹框 -->
    <AlertDialog v-model:open="deleteDialogOpen">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{{ deleteDialogContent.title }}</AlertDialogTitle>
          <AlertDialogDescription>
            {{ deleteDialogContent.description }}
            <br />
            <span class="text-muted-foreground text-sm">
              {{ deleteDialogContent.detail }}
            </span>
            <br />
            <span class="text-destructive text-sm font-medium">
              {{ deleteDialogContent.warning }}
            </span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="closeDeleteDialog">取消</AlertDialogCancel>
          <AlertDialogAction
            @click="confirmDeleteServer"
            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            :disabled="deleteLoading"
          >
            <MattIcon v-if="deleteLoading" name="Loader2" class="mr-2 h-4 w-4 animate-spin" />
            {{ deleteDialogContent.confirmText }}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, reactive } from 'vue'
import { toast } from 'vue-sonner'
import { useServerStore } from '@/store'
import { logger, serverService, formatDate, type Server } from '@mattverse/shared'
import {
  MattSearchForm,
  MattTable,
  MattPagination,
  type MattTableColumn,
  MattTableAction,
  MattSearchFormField,
} from '@mattverse/mattverse-ui'

// 使用 store
const serverStore = useServerStore()

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const refreshInterval = ref(serverStore.refreshTimer)
const refreshTimer = ref<NodeJS.Timeout | null>(null)

// 删除相关状态
const deleteDialogOpen = ref(false)
const deleteLoading = ref(false)
const serverToDelete = ref<Server | null>(null)
const isBatchDelete = ref(false) // 是否为批量删除

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox' as const,
  selectedRowKeys: [] as (string | number)[],
  onChange: (keys: (string | number)[], rows: Server[]) => {
    logger.info('选中的服务器变化', { count: keys.length, rowCount: rows.length, keys })
    // 同步更新选中状态
    rowSelection.selectedRowKeys = keys
  },
})

// 排序状态
const sortState = ref<Record<string, 'asc' | 'desc' | null>>({
  createTime: null,
  updateTime: null,
})

// 批量操作按钮配置
const batchActions = computed(() => [
  {
    key: 'close',
    label: '批量关闭',
    icon: 'Power',
    variant: 'outline' as const,
  },
  {
    key: 'restart',
    label: '批量重启',
    icon: 'RotateCcw',
    variant: 'outline' as const,
  },
  {
    key: 'pause',
    label: '批量暂停',
    icon: 'Pause',
    variant: 'outline' as const,
  },
  {
    key: 'delete',
    label: '批量删除',
    icon: 'Trash2',
    variant: 'destructive' as const,
  },
])

// 单个服务器操作配置
const serverActions = computed(() => [
  {
    key: 'close',
    label: '关闭',
    icon: 'Power',
    disabled: (record: Server) => record.serverStatus === 'Stopped',
  },
  {
    key: 'restart',
    label: '重启',
    icon: 'RotateCcw',
    disabled: (record: Server) => record.serverStatus === 'Stopped',
  },
  {
    key: 'pause',
    label: '暂停',
    icon: 'Pause',
    disabled: true,
  },
  {
    key: 'delete',
    label: '删除',
    icon: 'Trash2',
    variant: 'destructive' as const,
    separator: true, // 在此项前添加分隔线
  },
])

// 搜索表单数据
const searchForm = ref({
  keyword: '', // 搜索关键词（服务器名称、类型、ID）
  serverType: 'all', // 服务器类型筛选
  serverStatus: 'all', // 服务器状态筛选
})

// 搜索表单字段配置
const searchFields = computed<MattSearchFormField[]>(() => [
  {
    name: 'keyword',
    type: 'input',
    label: '搜索',
    placeholder: '搜索服务器名称、类型或ID',
    icon: 'search',
    width: 'w-full sm:min-w-[280px] sm:max-w-[320px]',
  },
  {
    name: 'serverType',
    type: 'select',
    label: '服务器类型',
    placeholder: '选择服务器类型',
    options: [
      { label: '全部类型', value: 'all' },
      { label: 'Agent服务器', value: 'agentServer' },
      { label: 'ML服务器', value: 'mlServer' },
      { label: 'Batt服务器', value: 'battServer' },
    ],
    width: 'w-full sm:min-w-[160px] sm:max-w-[200px]',
  },
  {
    name: 'serverStatus',
    type: 'select',
    label: '服务器状态',
    placeholder: '选择服务器状态',
    options: [
      { label: '全部状态', value: 'all' },
      { label: '正常运行', value: 'Running' },
      { label: '已停止', value: 'Stopped' },
      { label: '已过期', value: 'Expired' },
      { label: '服务器超载', value: 'Overloaded' },
      { label: '原始状态', value: 'Stay' },
    ],
    width: 'w-full sm:min-w-[160px] sm:max-w-[200px]',
  },
])

// 表格列配置
const tableColumns = computed<MattTableColumn[]>(() => [
  {
    key: 'serverName',
    title: '服务器名称',
    width: 150,
    slot: true,
    align: 'center',
    ellipsis: true,
  },
  {
    key: 'serverType',
    title: '服务器类型',
    width: 120,
    slot: true,
    align: 'center',
  },
  {
    key: 'serverId',
    title: '服务器ID',
    width: 200,
    slot: true,
    align: 'center',
    ellipsis: true,
  },
  {
    key: 'url',
    title: '服务器地址',
    width: 200,
    align: 'center',
    ellipsis: true,
  },
  {
    key: 'serverStatus',
    title: '服务器状态',
    width: 120,
    slot: true,
    align: 'center',
  },
  {
    key: 'region',
    title: '所在地区',
    width: 100,
    align: 'center',
  },
  {
    key: 'version',
    title: '服务器版本',
    width: 150,
    align: 'center',
  },
  {
    key: 'createTime',
    title: '注册时间',
    width: 150,
    slot: true,
    align: 'center',
    sortable: true,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    slot: true,
    align: 'center',
    sortable: true,
  },
])

// 表格操作配置
const tableActions = computed<MattTableAction[]>(() => [])

// 计算属性
const filteredData = computed(() => {
  let data = serverStore.servers

  // 关键词搜索
  if (searchForm.value.keyword) {
    const keyword = searchForm.value.keyword.toLowerCase()
    data = data.filter(
      server =>
        server.serverName.toLowerCase().includes(keyword) ||
        server.serverType.toLowerCase().includes(keyword) ||
        server.serverId.toLowerCase().includes(keyword)
    )
  }

  // 服务器类型筛选
  if (searchForm.value.serverType && searchForm.value.serverType !== 'all') {
    data = data.filter(server => server.serverType === searchForm.value.serverType)
  }

  // 服务器状态筛选
  if (searchForm.value.serverStatus && searchForm.value.serverStatus !== 'all') {
    data = data.filter(server => server.serverStatus === searchForm.value.serverStatus)
  }

  return data
})

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

// 删除弹框动态内容
const deleteDialogContent = computed(() => {
  if (isBatchDelete.value) {
    const selectedCount = rowSelection.selectedRowKeys.length
    return {
      title: '确认批量删除服务器',
      description: `您确定要删除选中的 ${selectedCount} 台服务器吗？`,
      detail: '此操作将删除所有选中的服务器',
      warning: '此操作不可撤销，请谨慎操作！',
      confirmText: deleteLoading.value ? '删除中...' : '确认删除',
    }
  } else if (serverToDelete.value) {
    return {
      title: '确认删除服务器',
      description: `您确定要删除服务器 "${serverToDelete.value.serverName}" 吗？`,
      detail: `服务器ID: ${serverToDelete.value.serverId}`,
      warning: '此操作不可撤销，请谨慎操作！',
      confirmText: deleteLoading.value ? '删除中...' : '确认删除',
    }
  }
  return {
    title: '确认删除',
    description: '',
    detail: '',
    warning: '',
    confirmText: '确认删除',
  }
})

// 工具方法
const getServerTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    agentServer: 'Agent服务器',
    mlServer: 'ML服务器',
    battServer: 'Batt服务器',
  }
  return typeMap[type] || type
}

const getServerTypeBadgeVariant = (
  type: string
): 'default' | 'secondary' | 'outline' | 'destructive' => {
  const variantMap: Record<string, 'default' | 'secondary' | 'outline' | 'destructive'> = {
    agentServer: 'default',
    mlServer: 'secondary',
    battServer: 'outline',
  }
  return variantMap[type] || 'default'
}

const { getStatusText, getStatusClass } = serverStore

// 格式化时间戳为日期时间字符串
const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return '-'

  // 将时间戳（秒）转换为毫秒
  const date = new Date(parseInt(timestamp) * 1000)

  return formatDate(date, 'YYYY/MM/DD HH:mm:ss')
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success(`已复制: ${text}`)
  } catch (err) {
    logger.error('复制失败:', err)
    toast.error('复制失败')
  }
}

// 事件处理方法
const handleSearch = (formData: Record<string, any>) => {
  searchForm.value = {
    keyword: formData.keyword || '',
    serverType: formData.serverType || 'all',
    serverStatus: formData.serverStatus || 'all',
  }
  currentPage.value = 1 // 重置到第一页
}

const handleReset = () => {
  searchForm.value = {
    keyword: '',
    serverType: 'all',
    serverStatus: 'all',
  }
  currentPage.value = 1
}

const handlePageChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
}

const handleRefreshIntervalChange = () => {
  serverStore.setRefreshTimer(refreshInterval.value)
  setupAutoRefresh()
}

const handleRefresh = async () => {
  loading.value = true
  try {
    await serverStore.updateServerList()
    toast.success('服务器列表已刷新')
  } catch (error) {
    logger.error('刷新服务器列表失败:', error)
    toast.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 统一的操作处理方法
const handleAction = async (action: string, server?: Server) => {
  const actionMap: Record<string, string> = {
    close: '关闭',
    restart: '重启',
    pause: '暂停',
  }

  const actionName = actionMap[action] || action

  try {
    // 这里应该调用相应的 API 来执行服务器操作
    // 由于没有具体的 API 接口，这里只是模拟
    if (server) {
      toast.info(`正在${actionName}服务器: ${server.serverName}`)
    }

    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 刷新服务器列表
    await serverStore.updateServerList()

    if (server) {
      toast.success(`${actionName}服务器成功: ${server.serverName}`)
    }
  } catch (error) {
    logger.error(`${actionName}服务器失败:`, error)
    toast.error(`${actionName}服务器失败`)
  }
}

// 单个服务器操作点击处理
const handleServerActionClick = async (action: string, record: any) => {
  const server = record as Server
  if (action === 'delete') {
    openDeleteDialog(server)
  } else {
    await handleAction(action, server)
  }
}

// 删除相关方法
const openDeleteDialog = (server: Server) => {
  serverToDelete.value = server
  isBatchDelete.value = false
  deleteDialogOpen.value = true
}

const openBatchDeleteDialog = () => {
  serverToDelete.value = null
  isBatchDelete.value = true
  deleteDialogOpen.value = true
}

const closeDeleteDialog = () => {
  deleteDialogOpen.value = false
  serverToDelete.value = null
  isBatchDelete.value = false
  deleteLoading.value = false
}

const confirmDeleteServer = async () => {
  deleteLoading.value = true

  try {
    if (isBatchDelete.value) {
      // 批量删除
      const selectedCount = rowSelection.selectedRowKeys.length
      if (selectedCount === 0) {
        toast.warning('请先选择要删除的服务器')
        return
      }

      toast.info(`正在批量删除 ${selectedCount} 台服务器...`)

      // 这里应该调用批量删除 API
      // 由于没有具体的 API 接口，这里只是模拟
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 刷新服务器列表
      await serverStore.updateServerList()

      // 清空选中状态
      rowSelection.selectedRowKeys = []

      toast.success(`批量删除 ${selectedCount} 台服务器成功`)
    } else {
      // 单个删除
      if (!serverToDelete.value) return

      // 调用删除服务器 API
      const response = await serverService.deleteServerInfo(serverToDelete.value.serverId)

      if (response.status === 'Success') {
        toast.success(`删除服务器成功: ${serverToDelete.value.serverName}`)

        // 刷新服务器列表
        await serverStore.updateServerList()
      } else {
        throw new Error(response.message || '删除服务器失败')
      }
    }

    // 关闭弹框
    closeDeleteDialog()
  } catch (error) {
    logger.error('删除服务器失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    toast.error(`删除服务器失败: ${errorMessage}`)
  } finally {
    deleteLoading.value = false
  }
}

// 批量操作处理方法
const handleBatchAction = async (action: string) => {
  if (rowSelection.selectedRowKeys.length === 0) {
    toast.warning('请先选择要操作的服务器')
    return
  }

  const actionMap: Record<string, string> = {
    close: '关闭',
    restart: '重启',
    pause: '暂停',
    delete: '删除',
  }

  const actionName = actionMap[action] || action
  const selectedCount = rowSelection.selectedRowKeys.length

  // 删除操作使用弹框确认
  if (action === 'delete') {
    openBatchDeleteDialog()
    return
  }

  try {
    toast.info(`正在批量${actionName} ${selectedCount} 台服务器...`)

    // 这里应该调用相应的批量操作 API
    // 由于没有具体的 API 接口，这里只是模拟
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 刷新服务器列表
    await serverStore.updateServerList()

    // 清空选中状态
    rowSelection.selectedRowKeys = []

    toast.success(`批量${actionName}服务器成功`)
  } catch (error) {
    logger.error(`批量${actionName}服务器失败:`, error)
    toast.error(`批量${actionName}服务器失败`)
  }
}

// 选择变化处理方法
const handleSelectionChange = (keys: (string | number)[], rows: Server[]) => {
  logger.info('表格选择变化', { count: keys.length, rowCount: rows.length, keys })
  rowSelection.selectedRowKeys = keys
}

// 排序变化处理方法
const handleSortChange = (key: string, order: 'asc' | 'desc' | null) => {
  logger.info('表格排序变化', { field: key, order })
  sortState.value[key] = order
}

// 自动刷新设置
const setupAutoRefresh = () => {
  // 清除现有定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }

  // 设置新的定时器
  if (refreshInterval.value > 0) {
    refreshTimer.value = setInterval(() => {
      handleRefresh()
    }, refreshInterval.value * 1000)
  }
}

// 监听刷新间隔变化
watch(
  () => refreshInterval.value,
  () => {
    serverStore.setRefreshTimer(refreshInterval.value)
    setupAutoRefresh()
  }
)

// 生命周期钩子
onMounted(async () => {
  // 初始化加载服务器列表
  await handleRefresh()

  // 设置自动刷新
  setupAutoRefresh()
})

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
})
</script>

<style lang="scss" scoped>
/* 页面特定样式 */

// /* 调整复选框列宽度 */
// :deep(.matt-table-wrapper) {
//   /* 复选框列头 */
//   th:first-child {
//     width: 48px !important;
//     min-width: 48px !important;
//     max-width: 48px !important;
//   }

//   /* 复选框列单元格 */
//   td:first-child {
//     width: 48px !important;
//     min-width: 48px !important;
//     max-width: 48px !important;
//   }
// }
</style>
