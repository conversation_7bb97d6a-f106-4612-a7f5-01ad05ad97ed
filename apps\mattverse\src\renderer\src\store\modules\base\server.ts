import { defineStore } from 'pinia'
import { ref } from 'vue'
import { createPersistConfig } from '@/store/plugins/persist-config'
import { serverService ,type Server,ServerUsage,ServerStatus } from '@mattverse/shared'
/**
 * 服务器状态管理
 */
export const useServerStore = defineStore(
  'server',
  () => {
    const loadingUsage = ref(false)
    const refreshTimer = ref<number>(5) // 服务列表刷新间隔（秒）
    const servers = ref<Server[]>([])
    const serverUsages = ref<ServerUsage[]>([])


    //刷新服务器列表
    const updateServerList = async (serverId?: string) => {
      const res = await serverService.getServerList(serverId)
      if (res.status === 'Success' && res.serverResult?.serverList) {
        servers.value = res.serverResult.serverList
      }
    }
    
     // 获取状态显示文本
    const getStatusText = (status: ServerStatus) => {
      const statusText: Record<ServerStatus, string> = {
        Running: '正常运行',
        Stopped: '已停止运行',
        Expired: '已过期',
        Overloaded: '服务器超载',
        Stay: '原始状态',
      }
      return statusText[status] || status
    }

    // 获取状态对应的样式
    const getStatusClass = (status: ServerStatus) => {
      const classes: Record<ServerStatus, string> = {
        Running: 'bg-green-500 hover:bg-green-600 text-white',
        Stopped: 'bg-red-500 hover:bg-red-600 text-white', 
        Expired: 'bg-amber-500 hover:bg-amber-600 text-white', 
        Overloaded: 'bg-orange-500 hover:bg-orange-600 text-white', 
        Stay: 'bg-slate-500 hover:bg-slate-600 text-white', 
      }
      return classes[status] || 'bg-slate-500 hover:bg-slate-600 text-white'
    }
     // 设置刷新间隔
    const setRefreshTimer = (interval: number) => {
      refreshTimer.value = interval
    }

    return {
      // 状态
      loadingUsage,
      servers,
      serverUsages,
      refreshTimer,
      // 计算属性

      // 方法
      getStatusText,
      getStatusClass,
      updateServerList,
      setRefreshTimer
    }
  },
  {
    persist: createPersistConfig('server'),
  }
)
