<template>
  <div class="p-6 space-y-6">
    <h1 class="text-2xl font-bold">Mattverse UI 组件测试</h1>

    <!-- 搜索表单示例 -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">搜索表单组件</h2>
      <MattSearchForm
        :fields="searchFields"
        :show-time-sort="true"
        :show-status-filter="true"
        :status-options="statusOptions"
        :show-filter-list="true"
        :filter-list-options="filterOptions"
        :collapsible="true"
        :show-labels="false"
        variant="default"
        :bordered="true"
        @search="handleSearch"
        @reset="handleReset"
        @time-sort-change="handleTimeSort"
        @status-change="handleStatusChange"
        @filter-change="handleFilterChange"
      />

      <!-- 展示不同风格的搜索表单 -->
      <div class="mt-6 space-y-4">
        <h3 class="text-lg font-medium">紧凑风格</h3>
        <MattSearchForm
          :fields="searchFields.slice(0, 2)"
          :show-time-sort="false"
          :show-status-filter="false"
          :show-filter-list="false"
          :show-labels="false"
          variant="compact"
          :bordered="true"
          @search="handleSearch"
          @reset="handleReset"
        />
      </div>

      <div class="mt-6 space-y-4">
        <h3 class="text-lg font-medium">最小化风格</h3>
        <MattSearchForm
          :fields="[searchFields[0]]"
          :show-time-sort="false"
          :show-status-filter="false"
          :show-filter-list="false"
          :show-reset="false"
          :show-labels="false"
          variant="minimal"
          :bordered="false"
          @search="handleSearch"
        />
      </div>
    </div>

    <!-- 表格组件示例 -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">表格组件</h2>
      <MattTable
        :data="tableData"
        :columns="tableColumns"
        :visible-columns="visibleColumns"
        :show-column-settings="true"
        :row-selection="rowSelection"
        :loading="loading"
        :max-height="400"
        hoverable
        striped
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @column-visibility-change="handleColumnVisibilityChange"
      >
        <!-- 左上角插槽 - 显示选中信息 -->
        <template #header-left>
          <div class="flex items-center gap-4">
            <h3 class="text-lg font-medium">服务列表</h3>
            <div
              v-if="rowSelection.selectedRowKeys.length > 0"
              class="text-sm text-muted-foreground"
            >
              已选择 {{ rowSelection.selectedRowKeys.length }} 个服务
            </div>
          </div>
        </template>

        <!-- 右上角插槽 - 批量操作按钮 -->
        <template #header-right>
          <div class="flex items-center gap-2">
            <Button
              v-if="rowSelection.selectedRowKeys.length > 0"
              variant="outline"
              size="sm"
              @click="handleBatchStart"
            >
              <Play class="h-4 w-4 mr-1" />
              批量启动
            </Button>
            <Button
              v-if="rowSelection.selectedRowKeys.length > 0"
              variant="outline"
              size="sm"
              @click="handleBatchStop"
            >
              <Square class="h-4 w-4 mr-1" />
              批量停止
            </Button>
            <Button variant="outline" size="sm" @click="handleRefresh">
              <RotateCcw class="h-4 w-4 mr-1" />
              刷新
            </Button>
          </div>
        </template>
        <!-- 自定义状态列 -->
        <template #status="{ record }">
          <span
            :class="[
              'px-2 py-1 rounded-full text-xs font-medium',
              record.status === 'running'
                ? 'bg-green-100 text-green-800'
                : record.status === 'stopped'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-gray-100 text-gray-800',
            ]"
          >
            {{ getStatusLabel(record.status) }}
          </span>
        </template>

        <!-- 自定义操作列 -->
        <template #actions="{ record, index }">
          <DropdownMenu>
            <DropdownMenuTrigger as-child class="cursor-pointer">
              <Ellipsis class="w-4 h-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                v-if="record.status === 'stopped'"
                @click="handleDropdown('start', record)"
              >
                <Play class="w-4 h-4" />
                <span>启动</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                v-if="record.status === 'running'"
                @click="handleDropdown('stop', record)"
              >
                <Square class="w-4 h-4" />
                <span>停止</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                v-if="record.status === 'running'"
                @click="handleDropdown('restart', record)"
              >
                <RotateCcw class="w-4 h-4" />
                <span>重启</span>
              </DropdownMenuItem>
              <DropdownMenuItem @click="handleDropdown('delete', record, index)">
                <Trash class="w-4 h-4" />
                <span>删除</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </template>
      </MattTable>
    </div>

    <!-- 分页组件示例 -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">分页组件</h2>
      <MattPagination
        :current="pagination.current"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="true"
        @update:current="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @change="handlePaginationChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Ellipsis, Play, Square, RotateCcw, Trash } from 'lucide-vue-next'
import { MattSearchForm, MattTable, MattPagination, Button } from '@mattverse/mattverse-ui'
import type { MattTableColumn, MattSearchFormField } from '@mattverse/mattverse-ui'

// 搜索表单配置
const searchFields: MattSearchFormField[] = [
  {
    name: 'name',
    label: '服务名称',
    type: 'input',
    placeholder: '搜索服务名称...',
    icon: 'search',
    showLabel: false,
  },
  {
    name: 'status',
    label: '服务状态',
    type: 'select',
    placeholder: '选择状态',
    showLabel: false,
    options: [
      { label: '运行中', value: 'running', icon: 'play' },
      { label: '已停止', value: 'stopped', icon: 'square' },
      { label: '错误', value: 'error', icon: 'alert-circle' },
    ],
  },
  {
    name: 'port',
    label: '端口号',
    type: 'number',
    placeholder: '端口号',
    showLabel: false,
    width: 'min-w-[120px]',
  },
  {
    name: 'createTime',
    label: '创建时间',
    type: 'daterange',
    showLabel: false,
    icon: 'calendar',
  },
]

const statusOptions = [
  { label: '运行中', value: 'running', icon: 'play' },
  { label: '已停止', value: 'stopped', icon: 'square' },
  { label: '错误', value: 'error', icon: 'alert-circle' },
]

const filterOptions = [
  { label: '今日创建', value: 'today', icon: 'calendar' },
  { label: '本周创建', value: 'thisWeek', icon: 'calendar-days' },
  { label: '最近活跃', value: 'recentActive', icon: 'activity' },
]

// 表格配置
const tableColumns: MattTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    sortable: true,
    hideable: false,
    defaultVisible: true,
  },
  {
    title: '服务名称',
    key: 'name',
    width: 150,
    copyable: true,
    hideable: false,
    defaultVisible: true,
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: true,
    tooltip: true,
    hideable: true,
    defaultVisible: true,
  },
  {
    title: '端口',
    key: 'port',
    width: 100,
    align: 'center',
    hideable: true,
    defaultVisible: true,
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    slot: true,
    align: 'center',
    hideable: false,
    defaultVisible: true,
  },
  {
    title: 'CPU使用率',
    key: 'cpuUsage',
    width: 120,
    formatter: value => `${value}%`,
    align: 'right',
    hideable: true,
    defaultVisible: true,
  },
  {
    title: '内存使用',
    key: 'memoryUsage',
    width: 120,
    formatter: value => `${value}MB`,
    align: 'right',
    hideable: true,
    defaultVisible: true,
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 150,
    sortable: true,
    hideable: true,
    defaultVisible: false,
  },
]

// 可见列状态 - 初始化为默认可见的列
const visibleColumns = ref<string[]>(
  tableColumns.filter(column => column.defaultVisible !== false).map(column => column.key)
)

// 数据
const loading = ref(false)
const tableData = ref([
  {
    id: 1,
    name: 'Web服务器',
    description: '主要的Web应用服务器，处理HTTP请求',
    port: 8080,
    status: 'running',
    cpuUsage: 15.6,
    memoryUsage: 512,
    createTime: '2024-01-15 10:30:00',
  },
  {
    id: 2,
    name: '数据库服务',
    description: 'MySQL数据库服务，存储应用数据',
    port: 3306,
    status: 'running',
    cpuUsage: 8.2,
    memoryUsage: 1024,
    createTime: '2024-01-15 09:15:00',
  },
  {
    id: 3,
    name: 'Redis缓存',
    description: '内存缓存服务，提高应用性能',
    port: 6379,
    status: 'stopped',
    cpuUsage: 0,
    memoryUsage: 0,
    createTime: '2024-01-15 11:45:00',
  },
  {
    id: 4,
    name: 'API网关',
    description: '统一API入口，负责路由和认证',
    port: 9000,
    status: 'error',
    cpuUsage: 0,
    memoryUsage: 256,
    createTime: '2024-01-16 14:20:00',
  },
  {
    id: 5,
    name: '消息队列',
    description: 'RabbitMQ消息队列服务',
    port: 5672,
    status: 'running',
    cpuUsage: 3.8,
    memoryUsage: 128,
    createTime: '2024-01-16 16:10:00',
  },
  {
    id: 6,
    name: 'Nginx代理',
    description: '反向代理服务器，负载均衡',
    port: 80,
    status: 'running',
    cpuUsage: 2.1,
    memoryUsage: 64,
    createTime: '2024-01-17 08:30:00',
  },
  {
    id: 7,
    name: 'ElasticSearch',
    description: '搜索引擎服务，全文检索',
    port: 9200,
    status: 'running',
    cpuUsage: 12.5,
    memoryUsage: 2048,
    createTime: '2024-01-17 09:45:00',
  },
  {
    id: 8,
    name: 'Kibana',
    description: '数据可视化平台',
    port: 5601,
    status: 'stopped',
    cpuUsage: 0,
    memoryUsage: 0,
    createTime: '2024-01-17 10:15:00',
  },
  {
    id: 9,
    name: 'MongoDB',
    description: 'NoSQL文档数据库',
    port: 27017,
    status: 'running',
    cpuUsage: 6.8,
    memoryUsage: 512,
    createTime: '2024-01-18 11:20:00',
  },
  {
    id: 10,
    name: 'Docker Registry',
    description: '私有镜像仓库',
    port: 5000,
    status: 'running',
    cpuUsage: 1.2,
    memoryUsage: 256,
    createTime: '2024-01-18 13:40:00',
  },
  {
    id: 11,
    name: 'Jenkins',
    description: 'CI/CD持续集成服务',
    port: 8081,
    status: 'running',
    cpuUsage: 18.9,
    memoryUsage: 1536,
    createTime: '2024-01-19 14:25:00',
  },
  {
    id: 12,
    name: 'Grafana',
    description: '监控数据可视化',
    port: 3000,
    status: 'running',
    cpuUsage: 4.3,
    memoryUsage: 128,
    createTime: '2024-01-19 15:50:00',
  },
])

const rowSelection = reactive({
  type: 'checkbox' as const,
  selectedRowKeys: [] as number[],
  onChange: (keys: number[], rows: any[]) => {
    console.log('选中的服务', keys, rows)
    // 同步更新选中状态
    rowSelection.selectedRowKeys = keys as number[]
  },
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 50,
})

// 方法
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    running: '运行中',
    stopped: '已停止',
    error: '错误',
  }
  return statusMap[status] || status
}

const handleSearch = (values: Record<string, any>) => {
  console.log('搜索服务', values)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    // 这里可以根据搜索条件过滤数据
  }, 1000)
}

const handleReset = () => {
  console.log('重置搜索条件')
}

const handleTimeSort = (order: 'asc' | 'desc' | null) => {
  console.log('时间排序', order)
}

const handleStatusChange = (status: any) => {
  console.log('状态筛选', status)
}

const handleFilterChange = (filter: any) => {
  console.log('筛选条件', filter)
}

const handleRowClick = (record: any, index: number) => {
  console.log('点击服务行', record, index)
}

const handleSelectionChange = (keys: (string | number)[], rows: any[]) => {
  console.log('服务选择变化', keys, rows)
}

const handleSortChange = (key: string, order: 'asc' | 'desc' | null) => {
  console.log('排序变化', key, order)
}

const handlePageChange = (page: number) => {
  pagination.current = page
  console.log('页码变化', page)
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  console.log('页大小变化', pageSize)
}

const handlePaginationChange = (page: number, pageSize: number) => {
  console.log('分页变化', page, pageSize)
}

const handleColumnVisibilityChange = (columns: string[]) => {
  console.log('列显示变化', columns)
  visibleColumns.value = columns
}

// 批量操作方法
const handleBatchStart = () => {
  const selectedServices = tableData.value.filter((_, index) =>
    rowSelection.selectedRowKeys.includes(index + 1)
  )
  selectedServices.forEach(service => {
    if (service.status === 'stopped') {
      service.status = 'running'
    }
  })
  console.log('批量启动服务', selectedServices)
}

const handleBatchStop = () => {
  const selectedServices = tableData.value.filter((_, index) =>
    rowSelection.selectedRowKeys.includes(index + 1)
  )
  selectedServices.forEach(service => {
    if (service.status === 'running') {
      service.status = 'stopped'
    }
  })
  console.log('批量停止服务', selectedServices)
}

const handleRefresh = () => {
  console.log('刷新服务列表')
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const handleDropdown = (action: string, record: any, index?: number) => {
  console.log('下拉操作', action, record, index)

  switch (action) {
    case 'start':
      record.status = 'running'
      break
    case 'stop':
      record.status = 'stopped'
      break
    case 'restart':
      // 模拟重启
      record.status = 'stopped'
      setTimeout(() => {
        record.status = 'running'
      }, 1000)
      break
    case 'delete':
      if (index !== undefined && confirm('确定要删除这个服务吗？此操作不可恢复。')) {
        tableData.value.splice(index, 1)
      }
      break
  }
}
</script>

<style scoped>
/* 自定义样式 */
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
